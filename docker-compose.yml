version: '3.8'

services:
    # PHP Service
    app:
        build:
            context: .
            dockerfile: Dockerfile
        image: laravel-app
        container_name: laravel-app
        restart: unless-stopped
        tty: true
        environment:
            SERVICE_NAME: app
            SERVICE_TAGS: dev
        working_dir: /var/www
        volumes:
            - ./:/var/www
            - ./docker/php/local.ini:/usr/local/etc/php/conf.d/local.ini
        networks:
            - laravel

    # Nginx Service
    webserver:
        image: nginx:alpine
        container_name: laravel-nginx
        restart: unless-stopped
        tty: true
        ports:
        - "8080:80"
        - "8443:443"
        volumes:
            - ./:/var/www
            - ./docker/nginx/conf.d/:/etc/nginx/conf.d/
        networks:
            - laravel

    # PostgreSQL Service
    db:
        image: postgres:15
        container_name: laravel-postgres
        restart: unless-stopped
        tty: true
        ports:
        - "5433:5432"
        environment:
            POSTGRES_DB: laravel
            POSTGRES_USER: laravel
            POSTGRES_PASSWORD: laravel
            SERVICE_TAGS: dev
            SERVICE_NAME: postgres
        volumes:
            - dbdata:/var/lib/postgresql/data
        networks:
            - laravel

# Docker Networks
networks:
    laravel:
        driver: bridge

# Volumes
volumes:
    dbdata:
        driver: local
