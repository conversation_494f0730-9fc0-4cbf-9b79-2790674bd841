{"openapi": "3.0.0", "info": {"title": "API Документация", "description": "Документация для API справочника", "version": "1.0.0"}, "paths": {"/api/buildings": {"get": {"tags": ["Buildings"], "summary": "Получить список зданий", "description": "Возвращает список всех зданий", "operationId": "getBuildingsList", "responses": {"200": {"description": "Успешный ответ", "content": {"application/json": {"schema": {"type": "array", "items": {"properties": {"id": {"type": "integer", "example": 1}, "address": {"type": "string", "example": "123 Main St"}, "latitude": {"type": "number", "format": "float", "example": 40.7128}, "longitude": {"type": "number", "format": "float", "example": -74.006}}, "type": "object"}}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"properties": {"message": {"type": "string", "example": "Unauthenticated."}}, "type": "object"}}}}}, "security": [{"bearerAuth": []}]}}, "/api/organizations/{id}": {"get": {"tags": ["Organizations"], "summary": "Показать организацию", "description": "Получить организацию по ID", "operationId": "getOrganizationById", "parameters": [{"name": "id", "in": "path", "description": "ID организации", "required": true, "schema": {"type": "integer", "example": 1}}], "responses": {"200": {"description": "Успешный ответ", "content": {"application/json": {"schema": {"properties": {"id": {"type": "integer", "example": 1}, "name": {"type": "string", "example": "Организация \"Пример\""}, "building": {"properties": {"id": {"type": "integer", "example": 1}, "address": {"type": "string", "example": "123 Main St"}, "latitude": {"type": "number", "format": "float", "example": 40.7128}, "longitude": {"type": "number", "format": "float", "example": -74.006}}, "type": "object"}, "phones": {"type": "array", "items": {"properties": {"id": {"type": "integer", "example": 1}, "phone": {"type": "string", "example": "+7 (999) 123-45-67"}}, "type": "object"}}, "activities": {"type": "array", "items": {"properties": {"id": {"type": "integer", "example": 1}, "parent_id": {"type": "integer", "example": null, "nullable": true}, "name": {"type": "string", "example": "Медицинские услуги"}}, "type": "object"}}}, "type": "object"}}}}, "404": {"description": "Not Found", "content": {"application/json": {"schema": {"properties": {"message": {"type": "string", "example": "No query results for model"}}, "type": "object"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"properties": {"message": {"type": "string", "example": "Unauthenticated."}}, "type": "object"}}}}}, "security": [{"bearerAuth": []}]}}, "/api/organizations/building/{building_id}": {"get": {"tags": ["Organizations"], "summary": "Получить организации по зданию", "description": "Получить организации по ID здания", "operationId": "getOrganizationsByBuilding", "parameters": [{"name": "building_id", "in": "path", "description": "ID здания", "required": true, "schema": {"type": "integer", "example": 1}}], "responses": {"200": {"description": "Успешный ответ", "content": {"application/json": {"schema": {"type": "array", "items": {"properties": {"id": {"type": "integer", "example": 1}, "name": {"type": "string", "example": "Организация \"Пример\""}, "building": {"properties": {"id": {"type": "integer", "example": 1}, "address": {"type": "string", "example": "123 Main St"}, "latitude": {"type": "number", "format": "float", "example": 40.7128}, "longitude": {"type": "number", "format": "float", "example": -74.006}}, "type": "object"}, "phones": {"type": "array", "items": {"properties": {"id": {"type": "integer", "example": 1}, "phone": {"type": "string", "example": "+7 (999) 123-45-67"}}, "type": "object"}}, "activities": {"type": "array", "items": {"properties": {"id": {"type": "integer", "example": 1}, "parent_id": {"type": "integer", "example": null, "nullable": true}, "name": {"type": "string", "example": "Медицинские услуги"}}, "type": "object"}}}, "type": "object"}}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"properties": {"message": {"type": "string", "example": "Unauthenticated."}}, "type": "object"}}}}}, "security": [{"bearerAuth": []}]}}, "/api/organizations/activity/{activity_id}": {"get": {"tags": ["Organizations"], "summary": "Получить организации по деятельности", "description": "Получить организации по ID деятельности", "operationId": "getOrganizationsByActivity", "parameters": [{"name": "activity_id", "in": "path", "description": "ID деятельности", "required": true, "schema": {"type": "integer", "example": 1}}], "responses": {"200": {"description": "Успешный ответ", "content": {"application/json": {"schema": {"type": "array", "items": {"properties": {"id": {"type": "integer", "example": 1}, "name": {"type": "string", "example": "Организация \"Пример\""}, "building": {"properties": {"id": {"type": "integer", "example": 1}, "address": {"type": "string", "example": "123 Main St"}, "latitude": {"type": "number", "format": "float", "example": 40.7128}, "longitude": {"type": "number", "format": "float", "example": -74.006}}, "type": "object"}, "phones": {"type": "array", "items": {"properties": {"id": {"type": "integer", "example": 1}, "phone": {"type": "string", "example": "+7 (999) 123-45-67"}}, "type": "object"}}, "activities": {"type": "array", "items": {"properties": {"id": {"type": "integer", "example": 1}, "parent_id": {"type": "integer", "example": null, "nullable": true}, "name": {"type": "string", "example": "Медицинские услуги"}}, "type": "object"}}}, "type": "object"}}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"properties": {"message": {"type": "string", "example": "Unauthenticated."}}, "type": "object"}}}}}, "security": [{"bearerAuth": []}]}}, "/api/organizations/search/activity/tree": {"post": {"tags": ["Organizations"], "summary": "Поиск организаций по дереву деятельности", "description": "Поиск организаций по дереву деятельности", "operationId": "searchOrganizationsByActivityTree", "requestBody": {"required": true, "content": {"application/json": {"schema": {"required": ["activity_id"], "properties": {"activity_id": {"description": "ID родительского вида деятельности", "type": "integer", "example": 1}}, "type": "object"}}}}, "responses": {"200": {"description": "Успешный ответ", "content": {"application/json": {"schema": {"type": "array", "items": {"properties": {"id": {"type": "integer", "example": 1}, "name": {"type": "string", "example": "Организация \"Пример\""}, "building": {"properties": {"id": {"type": "integer", "example": 1}, "address": {"type": "string", "example": "123 Main St"}, "latitude": {"type": "number", "format": "float", "example": 40.7128}, "longitude": {"type": "number", "format": "float", "example": -74.006}}, "type": "object"}, "phones": {"type": "array", "items": {"properties": {"id": {"type": "integer", "example": 1}, "phone": {"type": "string", "example": "+7 (999) 123-45-67"}}, "type": "object"}}, "activities": {"type": "array", "items": {"properties": {"id": {"type": "integer", "example": 1}, "parent_id": {"type": "integer", "example": null, "nullable": true}, "name": {"type": "string", "example": "Медицинские услуги"}}, "type": "object"}}}, "type": "object"}}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"properties": {"message": {"type": "string", "example": "The given data was invalid."}, "errors": {"type": "object", "example": {"field_name": ["Field is required."]}}}, "type": "object"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"properties": {"message": {"type": "string", "example": "Unauthenticated."}}, "type": "object"}}}}}, "security": [{"bearerAuth": []}]}}, "/api/organizations/search/name": {"get": {"tags": ["Organizations"], "summary": "Поиск организаций по названию", "description": "Поиск организаций по названию", "operationId": "searchOrganizationsByName", "parameters": [{"name": "name", "in": "query", "description": "Название организации для поиска", "required": true, "schema": {"type": "string", "example": "Медицинский"}}], "responses": {"200": {"description": "Успешный ответ", "content": {"application/json": {"schema": {"type": "array", "items": {"properties": {"id": {"type": "integer", "example": 1}, "name": {"type": "string", "example": "Организация \"Пример\""}, "building": {"properties": {"id": {"type": "integer", "example": 1}, "address": {"type": "string", "example": "123 Main St"}, "latitude": {"type": "number", "format": "float", "example": 40.7128}, "longitude": {"type": "number", "format": "float", "example": -74.006}}, "type": "object"}, "phones": {"type": "array", "items": {"properties": {"id": {"type": "integer", "example": 1}, "phone": {"type": "string", "example": "+7 (999) 123-45-67"}}, "type": "object"}}, "activities": {"type": "array", "items": {"properties": {"id": {"type": "integer", "example": 1}, "parent_id": {"type": "integer", "example": null, "nullable": true}, "name": {"type": "string", "example": "Медицинские услуги"}}, "type": "object"}}}, "type": "object"}}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"properties": {"message": {"type": "string", "example": "The given data was invalid."}, "errors": {"type": "object", "example": {"field_name": ["Field is required."]}}}, "type": "object"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"properties": {"message": {"type": "string", "example": "Unauthenticated."}}, "type": "object"}}}}}, "security": [{"bearerAuth": []}]}}, "/api/organizations/search/geo/radius": {"post": {"tags": ["Organizations"], "summary": "Поиск организаций по геолокации (радиус)", "description": "Поиск организаций в радиусе от точки", "operationId": "getOrganizationsByGeoRadius", "requestBody": {"required": true, "content": {"application/json": {"schema": {"required": ["latitude", "longitude", "radius"], "properties": {"latitude": {"type": "number", "format": "float", "example": 55.7558}, "longitude": {"type": "number", "format": "float", "example": 37.6176}, "radius": {"description": "Радиус поиска в метрах", "type": "number", "format": "float", "example": 1000}}, "type": "object"}}}}, "responses": {"200": {"description": "Успешный ответ", "content": {"application/json": {"schema": {"type": "array", "items": {"properties": {"id": {"type": "integer", "example": 1}, "name": {"type": "string", "example": "Организация \"Пример\""}, "building": {"properties": {"id": {"type": "integer", "example": 1}, "address": {"type": "string", "example": "123 Main St"}, "latitude": {"type": "number", "format": "float", "example": 40.7128}, "longitude": {"type": "number", "format": "float", "example": -74.006}}, "type": "object"}, "phones": {"type": "array", "items": {"properties": {"id": {"type": "integer", "example": 1}, "phone": {"type": "string", "example": "+7 (999) 123-45-67"}}, "type": "object"}}, "activities": {"type": "array", "items": {"properties": {"id": {"type": "integer", "example": 1}, "parent_id": {"type": "integer", "example": null, "nullable": true}, "name": {"type": "string", "example": "Медицинские услуги"}}, "type": "object"}}}, "type": "object"}}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"properties": {"message": {"type": "string", "example": "The given data was invalid."}, "errors": {"type": "object", "example": {"field_name": ["Field is required."]}}}, "type": "object"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"properties": {"message": {"type": "string", "example": "Unauthenticated."}}, "type": "object"}}}}}, "security": [{"bearerAuth": []}]}}, "/api/organizations/search/geo/rectangle": {"post": {"tags": ["Organizations"], "summary": "Поиск организаций по геолокации (прямоугольник)", "description": "Поиск организаций в прямоугольной области", "operationId": "getOrganizationsByGeoRectangle", "requestBody": {"required": true, "content": {"application/json": {"schema": {"required": ["min_lat", "max_lat", "min_lng", "max_lng"], "properties": {"min_lat": {"description": "Минимальная широта", "type": "number", "format": "float", "example": 55.6558}, "max_lat": {"description": "Максимальная широта", "type": "number", "format": "float", "example": 55.8558}, "min_lng": {"description": "Минимальная долгота", "type": "number", "format": "float", "example": 37.5176}, "max_lng": {"description": "Максимальная долгота", "type": "number", "format": "float", "example": 37.7176}}, "type": "object"}}}}, "responses": {"200": {"description": "Успешный ответ", "content": {"application/json": {"schema": {"type": "array", "items": {"properties": {"id": {"type": "integer", "example": 1}, "name": {"type": "string", "example": "Организация \"Пример\""}, "building": {"properties": {"id": {"type": "integer", "example": 1}, "address": {"type": "string", "example": "123 Main St"}, "latitude": {"type": "number", "format": "float", "example": 40.7128}, "longitude": {"type": "number", "format": "float", "example": -74.006}}, "type": "object"}, "phones": {"type": "array", "items": {"properties": {"id": {"type": "integer", "example": 1}, "phone": {"type": "string", "example": "+7 (999) 123-45-67"}}, "type": "object"}}, "activities": {"type": "array", "items": {"properties": {"id": {"type": "integer", "example": 1}, "parent_id": {"type": "integer", "example": null, "nullable": true}, "name": {"type": "string", "example": "Медицинские услуги"}}, "type": "object"}}}, "type": "object"}}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"properties": {"message": {"type": "string", "example": "The given data was invalid."}, "errors": {"type": "object", "example": {"field_name": ["Field is required."]}}}, "type": "object"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"properties": {"message": {"type": "string", "example": "Unauthenticated."}}, "type": "object"}}}}}, "security": [{"bearerAuth": []}]}}, "/api/token": {"get": {"tags": ["<PERSON><PERSON>"], "summary": "Получить токен", "description": "Получить токен для доступа к API", "operationId": "getToken", "responses": {"200": {"description": "Токен успешно создан", "content": {"application/json": {"schema": {"properties": {"token": {"type": "string", "example": "1|abcdef123456789"}, "type": {"type": "string", "example": "Bearer"}}, "type": "object"}}}}}}}}, "components": {"schemas": {"Building": {"title": "Building", "required": ["id", "address", "latitude", "longitude"], "properties": {"id": {"type": "integer", "example": 1}, "address": {"type": "string", "example": "123 Main St"}, "latitude": {"type": "number", "format": "float", "example": 40.7128}, "longitude": {"type": "number", "format": "float", "example": -74.006}}, "type": "object"}, "Organization": {"title": "Organization", "required": ["id", "name"], "properties": {"id": {"type": "integer", "example": 1}, "name": {"type": "string", "example": "Организация \"Пример\""}, "building": {"$ref": "#/components/schemas/Building"}, "phones": {"type": "array", "items": {"properties": {"id": {"type": "integer", "example": 1}, "phone": {"type": "string", "example": "+7 (999) 123-45-67"}}, "type": "object"}}, "activities": {"type": "array", "items": {"properties": {"id": {"type": "integer", "example": 1}, "parent_id": {"type": "integer", "example": null, "nullable": true}, "name": {"type": "string", "example": "Медицинские услуги"}}, "type": "object"}}}, "type": "object"}}, "requestBodies": {"searchByActivityTree": {"required": true, "content": {"application/json": {"schema": {"required": ["activity_id"], "properties": {"activity_id": {"description": "ID родительского вида деятельности", "type": "integer", "example": 1}}, "type": "object"}}}}, "getByGeoRadius": {"required": true, "content": {"application/json": {"schema": {"required": ["latitude", "longitude", "radius"], "properties": {"latitude": {"type": "number", "format": "float", "example": 55.7558}, "longitude": {"type": "number", "format": "float", "example": 37.6176}, "radius": {"description": "Радиус поиска в метрах", "type": "number", "format": "float", "example": 1000}}, "type": "object"}}}}, "getByGeoRectangle": {"required": true, "content": {"application/json": {"schema": {"required": ["min_lat", "max_lat", "min_lng", "max_lng"], "properties": {"min_lat": {"description": "Минимальная широта", "type": "number", "format": "float", "example": 55.6558}, "max_lat": {"description": "Максимальная широта", "type": "number", "format": "float", "example": 55.8558}, "min_lng": {"description": "Минимальная долгота", "type": "number", "format": "float", "example": 37.5176}, "max_lng": {"description": "Максимальная долгота", "type": "number", "format": "float", "example": 37.7176}}, "type": "object"}}}}}, "securitySchemes": {"bearerAuth": {"type": "http", "bearerFormat": "JWT", "scheme": "bearer"}}}, "tags": [{"name": "<PERSON><PERSON>", "description": "Получение токена для API"}, {"name": "Buildings", "description": "Buildings"}, {"name": "Organizations", "description": "Organizations"}]}