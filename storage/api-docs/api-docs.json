{"openapi": "3.0.0", "info": {"title": "API Документация", "description": "Документация для API справочника", "version": "1.0.0"}, "paths": {"/api/buildings": {"get": {"tags": ["Buildings"], "summary": "Получить список зданий", "description": "Возвращает список всех зданий", "operationId": "getBuildingsList", "responses": {"200": {"description": "Успешный ответ", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Building"}}}}}}, "security": [{"bearerAuth": []}]}}, "/api/organizations/{id}": {"get": {"tags": ["Organizations"], "summary": "Показать организацию", "description": "Получить организацию по ID", "operationId": "getOrganizationById", "parameters": [{"name": "id", "in": "path", "description": "ID организации", "required": true, "schema": {"type": "integer", "example": 1}}], "responses": {"200": {"description": "Успешный ответ", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Organization"}}}}, "404": {"description": "Организация не найдена", "content": {"application/json": {"schema": {"properties": {"message": {"type": "string", "example": "No query results for model [App\\Models\\Organization] 1"}}, "type": "object"}}}}}, "security": [{"bearerAuth": []}]}}, "/api/organizations/building/{buildingId}": {"get": {"tags": ["Organizations"], "summary": "Организации по зданию", "description": "Получить организации в здании", "operationId": "getOrganizationsByBuilding", "parameters": [{"name": "buildingId", "in": "path", "description": "ID здания", "required": true, "schema": {"type": "integer", "example": 1}}], "responses": {"200": {"description": "Успешный ответ", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Organization"}}}}}}, "security": [{"bearerAuth": []}]}}, "/api/organizations/activity/{activityId}": {"get": {"tags": ["Organizations"], "summary": "Организации по деятельности", "description": "Получить организации по виду деятельности", "operationId": "getOrganizationsByActivity", "parameters": [{"name": "activityId", "in": "path", "description": "ID вида деятельности", "required": true, "schema": {"type": "integer", "example": 1}}], "responses": {"200": {"description": "Успешный ответ", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Organization"}}}}}}, "security": [{"bearerAuth": []}]}}, "/api/organizations/search/activity/tree": {"post": {"tags": ["Organizations"], "summary": "Поиск по дереву деятельности", "description": "Поиск организаций по дереву видов деятельности (включая дочерние)", "operationId": "searchOrganizationsByActivityTree", "requestBody": {"required": true, "content": {"application/json": {"schema": {"required": ["activity_id"], "properties": {"activity_id": {"description": "ID родительского вида деятельности", "type": "integer", "example": 1}}, "type": "object"}}}}, "responses": {"200": {"description": "Успешный ответ", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Organization"}}}}}, "422": {"description": "Ошибка валидации", "content": {"application/json": {"schema": {"properties": {"message": {"type": "string", "example": "The given data was invalid."}, "errors": {"properties": {"activity_id": {"type": "array", "items": {"type": "string"}}}, "type": "object"}}, "type": "object"}}}}}, "security": [{"bearerAuth": []}]}}, "/api/organizations/search/name": {"get": {"tags": ["Organizations"], "summary": "Поиск по названию", "description": "Поиск организаций по названию", "operationId": "searchOrganizationsByName", "parameters": [{"name": "name", "in": "query", "description": "Часть названия организации", "required": true, "schema": {"type": "string", "example": "медицина"}}], "responses": {"200": {"description": "Успешный ответ", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Organization"}}}}}, "422": {"description": "Ошибка валидации", "content": {"application/json": {"schema": {"properties": {"message": {"type": "string", "example": "The given data was invalid."}, "errors": {"properties": {"name": {"type": "array", "items": {"type": "string"}}}, "type": "object"}}, "type": "object"}}}}}, "security": [{"bearerAuth": []}]}}, "/api/organizations/search/geo/radius": {"post": {"tags": ["Organizations"], "summary": "Поиск по радиусу", "description": "Поиск организаций в радиусе от точки", "operationId": "getOrganizationsByGeoRadius", "requestBody": {"required": true, "content": {"application/json": {"schema": {"required": ["latitude", "longitude", "radius"], "properties": {"latitude": {"description": "Широта центра поиска", "type": "number", "format": "float", "example": 55.7558}, "longitude": {"description": "Долгота центра поиска", "type": "number", "format": "float", "example": 37.6176}, "radius": {"description": "Радиус поиска в километрах", "type": "number", "format": "float", "example": 5}}, "type": "object"}}}}, "responses": {"200": {"description": "Успешный ответ", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Organization"}}}}}, "422": {"description": "Ошибка валидации", "content": {"application/json": {"schema": {"properties": {"message": {"type": "string", "example": "The given data was invalid."}, "errors": {"properties": {"latitude": {"type": "array", "items": {"type": "string"}}, "longitude": {"type": "array", "items": {"type": "string"}}, "radius": {"type": "array", "items": {"type": "string"}}}, "type": "object"}}, "type": "object"}}}}}, "security": [{"bearerAuth": []}]}}, "/api/organizations/search/geo/rectangle": {"post": {"tags": ["Organizations"], "summary": "Поиск по прямоугольнику", "description": "Поиск организаций в прямоугольной области", "operationId": "getOrganizationsByGeoRectangle", "requestBody": {"required": true, "content": {"application/json": {"schema": {"required": ["min_lat", "max_lat", "min_lng", "max_lng"], "properties": {"min_lat": {"description": "Минимальная широта", "type": "number", "format": "float", "example": 55.5}, "max_lat": {"description": "Максимальная широта", "type": "number", "format": "float", "example": 56}, "min_lng": {"description": "Минимальная долгота", "type": "number", "format": "float", "example": 37}, "max_lng": {"description": "Максимальная долгота", "type": "number", "format": "float", "example": 38}}, "type": "object"}}}}, "responses": {"200": {"description": "Успешный ответ", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Organization"}}}}}, "422": {"description": "Ошибка валидации", "content": {"application/json": {"schema": {"properties": {"message": {"type": "string", "example": "The given data was invalid."}, "errors": {"properties": {"min_lat": {"type": "array", "items": {"type": "string"}}, "max_lat": {"type": "array", "items": {"type": "string"}}, "min_lng": {"type": "array", "items": {"type": "string"}}, "max_lng": {"type": "array", "items": {"type": "string"}}}, "type": "object"}}, "type": "object"}}}}}, "security": [{"bearerAuth": []}]}}, "/api/token": {"get": {"tags": ["<PERSON><PERSON>"], "summary": "Получить токен", "description": "Получить токен для доступа к API", "operationId": "getToken", "responses": {"200": {"description": "Токен успешно создан", "content": {"application/json": {"schema": {"properties": {"token": {"type": "string", "example": "1|abcdef123456789"}, "type": {"type": "string", "example": "Bearer"}}, "type": "object"}}}}}}}}, "components": {"schemas": {"Building": {"title": "Building", "required": ["id", "address", "latitude", "longitude"], "properties": {"id": {"type": "integer", "example": 1}, "address": {"type": "string", "example": "123 Main St"}, "latitude": {"type": "number", "format": "float", "example": 40.7128}, "longitude": {"type": "number", "format": "float", "example": -74.006}}, "type": "object"}, "Organization": {"title": "Organization", "required": ["id", "name"], "properties": {"id": {"type": "integer", "example": 1}, "name": {"type": "string", "example": "Организация \"Пример\""}, "building_id": {"type": "integer", "example": 1}, "description": {"type": "string", "example": "Описание организации"}, "building": {"$ref": "#/components/schemas/Building"}, "phones": {"type": "array", "items": {"properties": {"id": {"type": "integer", "example": 1}, "number": {"type": "string", "example": "+7 (999) 123-45-67"}}, "type": "object"}}, "activities": {"type": "array", "items": {"properties": {"id": {"type": "integer", "example": 1}, "name": {"type": "string", "example": "Медицинские услуги"}}, "type": "object"}}}, "type": "object"}}, "securitySchemes": {"bearerAuth": {"type": "http", "bearerFormat": "JWT", "scheme": "bearer"}}}, "tags": [{"name": "<PERSON><PERSON>", "description": "Получение токена для API"}, {"name": "Buildings", "description": "Buildings"}, {"name": "Organizations", "description": "Organizations"}]}